import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/todo_tile.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _userName = '';
  final List<Map<String, dynamic>> cards = [
    {'color': AppColors.blackColor, 'text': 'Card 1'},
    {'color': AppColors.blackColor, 'text': 'Card 2'},
    {'color': AppColors.blackColor, 'text': 'Card 3'},
    // Add more cards if you like
  ];

  @override
  void initState() {
    super.initState();
    _loadUserName();
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppSpacing.xl),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  height: 50,
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.md,
                  ),
                  decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [
                        Color(0xFFff4b1f),
                        Color(0xFFff9068),
                      ]),
                      borderRadius: BorderRadius.circular(AppBorderRadius.full),
                      border: Border.all(
                        color: AppColors.accentOrange,
                        width: 0.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.accentOrange.withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ]),
                  child: Row(
                    children: [
                      Icon(
                        CupertinoIcons.flame_fill,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        '3 streaks',
                        style: TextStyle(
                          color: AppColors.whiteColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                )
                    .animate()
                    .slideX(
                      begin: -0.3,
                      duration: 500.ms,
                      curve: Curves.easeOut,
                    )
                    .fadeIn(delay: 200.ms),
                Spacer(),
                Row(
                  children: [
                    Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.textMuted,
                          width: 0.5,
                        ),
                      ),
                      child: Icon(Icons.person_2_outlined),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.textMuted,
                          width: 0.5,
                        ),
                      ),
                      child: Icon(Icons.add),
                    ),
                  ],
                )
                    .animate()
                    .slideX(
                      begin: 0.3,
                      duration: 500.ms,
                      curve: Curves.easeOut,
                    )
                    .fadeIn(delay: 200.ms),
              ],
            ),

            const SizedBox(height: AppSpacing.md),
            // Welcome header
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Let's get to work,",
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )
                    .animate()
                    .slideX(
                      begin: -0.3,
                      duration: 500.ms,
                      curve: Curves.easeOut,
                    )
                    .fadeIn(),
                Text(
                  _userName,
                  style: AppTextStyles.h1.copyWith(
                    fontSize: 32,
                    fontWeight: FontWeight.w800,
                  ),
                )
                    .animate()
                    .slideX(
                      begin: -0.3,
                      duration: 500.ms,
                      curve: Curves.easeOut,
                    )
                    .fadeIn(delay: 200.ms),
              ],
            ),

            const SizedBox(height: AppSpacing.xl),
            Spacer(),
            Align(
              alignment: Alignment.bottomCenter,
              child: CardsSwiperWidget(
                cardData: cards,
                onCardCollectionAnimationComplete: (_) {},
                onCardChange: (index) {
                  print('Top card index: $index');
                },
                cardBuilder: (context, index, visibleIndex) {
                  final card = cards[index];
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(32),
                      color: card['color'] as Color,
                      border: Border.all(
                        color: AppColors.textMuted,
                        width: 0.5,
                      ),
                    ),
                    width: 600,
                    height: 250,
                    alignment: Alignment.center,
                    child: Text(
                      card['text'] as String,
                      style: const TextStyle(color: Colors.white, fontSize: 20),
                    ),
                  );
                },
              ),
            )
                .animate()
                .slideY(
                  begin: 0.3,
                  duration: 500.ms,
                  curve: Curves.easeOut,
                )
                .fadeIn(delay: 200.ms),
            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }
}
